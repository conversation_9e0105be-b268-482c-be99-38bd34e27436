# Deploy Notes - Fix Migration Checksum Error

## Changes Made

### 1. Updated `entrypoint.sh`
- Added automatic checksum fix before applying migrations
- Added logging for better debugging
- Now runs `atlas migrate hash --dir file://migrations` before `atlas migrate apply`

### 2. Updated `Makefile`
- Fixed `db-rehash` command to specify correct directory
- Added new `db-fix-checksum` command for easy checksum fixing
- Updated .PHONY targets

### 3. Created `scripts/fix-migration-checksum.sh`
- New helper script to fix checksum errors
- Includes validation and user-friendly output
- Can be run manually when needed

### 4. Updated `migrations/README.md`
- Added troubleshooting section for checksum errors
- Added deployment instructions
- Added available commands reference

## How to Deploy to Unstable

1. **Commit all changes:**
```bash
git add .
git commit -m "fix: resolve Atlas migration checksum error for unstable deployment

- Add automatic checksum fix in entrypoint.sh
- Update Makefile with db-fix-checksum command
- Add migration troubleshooting documentation
- Create helper script for manual checksum fixes"
```

2. **Push to unstable branch:**
```bash
git push origin unstable
```

## What Happens During Deployment

1. GitLab CI will build the Docker image with updated entrypoint.sh
2. When the pod starts, entrypoint.sh will:
   - Re-hash migration files to fix checksum errors
   - Apply pending migrations to the database
   - Start the application

## Manual Testing (Optional)

Before deploying, you can test locally:

```bash
# Test the fix script
make db-fix-checksum

# Test migration apply
make db-apply

# Test with Docker
docker-compose up --build
```

## Rollback Plan

If deployment fails, you can:
1. Revert the commit: `git revert HEAD`
2. Push to unstable: `git push origin unstable`
3. Or manually fix in the pod by running: `atlas migrate hash --dir file://migrations`
